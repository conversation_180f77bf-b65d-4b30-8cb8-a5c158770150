package com.fjdynamics.towermanager.network

import android.text.TextUtils
import com.blankj.utilcode.util.EncodeUtils
import com.blankj.utilcode.util.StringUtils
import com.fjdynamics.towermanager.App
import com.fjdynamics.towermanager.util.FileStore
import com.fjdynamics.towermanager.util.thisLogger
import me.jessyan.retrofiturlmanager.RetrofitUrlManager

/**
 * Api服务管理类
 *
 * <AUTHOR>
 */
object ApiManager {
	const val DOMAIN_PLATFORM: String = "domain_platform"
	const val DOMAIN_LARGE_SCREEN: String = "domain_large_screen"
	const val DOMAIN_FACE_RECOGNITION: String = "domain_face_recognition"
	private val logger = thisLogger()
	private var platformUrl: String = ""

	/**
	 * 刷新baseUrl
	 */
	fun refreshBaseUrl() {
		platformUrl =
			StringUtils.format("https://umtc-%s.fjdynamics.com/", if (App.IS_PROD) "prodhk" else "test")
		RetrofitUrlManager.getInstance().putDomain(DOMAIN_PLATFORM, platformUrl)

		val largeScreenIp = FileStore.getWebSocketServerPcIp()
		if (!TextUtils.isEmpty(largeScreenIp)) {
			RetrofitUrlManager.getInstance()
				.putDomain(DOMAIN_LARGE_SCREEN, "http://$largeScreenIp:8080/")
			RetrofitUrlManager.getInstance()
				.putDomain(DOMAIN_FACE_RECOGNITION, "http://$largeScreenIp:9200/")
		}
		logger.debug("refreshBaseUrl: platform -> {}, largeScreenIp -> {}", platformUrl, largeScreenIp)
	}

	/**
	 * 获取云平台图片预览url
	 *
	 * @param filePath 文件地址
	 * @return url
	 */
	fun getImagePreviewUrl(filePath: String?): String {
		val encodedFilePath = EncodeUtils.urlEncode(filePath)
		val encodedToken = EncodeUtils.urlEncode(FileStore.getAuthorization())
		return platformUrl + "api/file/preview/image?filePath=" + encodedFilePath + "&token=" + encodedToken
	}

	/**
	 * 获取云平台视频预览url
	 *
	 * @param filePath 文件地址
	 * @return url
	 */
	fun getVideoPreviewUrl(filePath: String?): String {
		val encodedFilePath = EncodeUtils.urlEncode(filePath)
		val encodedToken = EncodeUtils.urlEncode(FileStore.getAuthorization())
		return platformUrl + "api/file/preview?filePath=" + encodedFilePath + "&token=" + encodedToken
	}
}
