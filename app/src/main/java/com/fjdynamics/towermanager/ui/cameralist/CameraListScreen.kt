package com.fjdynamics.towermanager.ui.cameralist

import android.graphics.Bitmap
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.RadioButton
import androidx.compose.material3.RadioButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.fjdynamics.towermanager.R
import com.fjdynamics.towermanager.network.model.CameraInfo

/**
 * 视频墙页
 *
 * <AUTHOR>
 */
@Composable
fun CameraListScreen(
	viewModel: CameraListViewModel,
	onBackClick: () -> Unit,
	onCameraClick: (Int) -> Unit,
) {
	val uiState by viewModel.uiState.collectAsStateWithLifecycle()

	Box(
		modifier = Modifier
			.fillMaxSize()
			.background(Color.Black),
	) {
		LazyVerticalGrid(
			columns = GridCells.Fixed(4),
			modifier = Modifier
				.fillMaxSize()
				.padding(top = 26.dp, bottom = 20.dp),
			contentPadding = PaddingValues(horizontal = 8.dp),
			horizontalArrangement = Arrangement.spacedBy(8.dp),
			verticalArrangement = Arrangement.spacedBy(8.dp),
		) {
			itemsIndexed(
				items = uiState.cameraList.take(12), // 最多显示12个摄像头
				key = { index, camera -> "${camera.id}_$index" },
			) { index, camera ->
				CameraGridItem(
					camera = camera,
					bitmap = uiState.cameraImages[index],
					isSelected = index == uiState.selectedIndex,
					onCameraClick = {
						viewModel.onCameraClick(index)
						onCameraClick(index)
					},
					onFocusClick = { viewModel.focusCamera(index) },
					onZoomClick = { viewModel.zoomCamera(index) },
				)
			}
		}

		BackIconButton(
			onClick = onBackClick,
			modifier = Modifier.align(Alignment.TopStart),
		)
	}
}

@Composable
fun BackIconButton(
	onClick: () -> Unit,
	modifier: Modifier = Modifier,
) {
	val interactionSource = remember { MutableInteractionSource() }
	val isPressed by interactionSource.collectIsPressedAsState()

	val backgroundColor by animateColorAsState(
		targetValue = if (isPressed) {
			Color(0xCC000000)
		} else {
			Color(0xCC191923)
		},
		animationSpec = tween(durationMillis = 150),
		label = "background_color",
	)

	IconButton(
		onClick = onClick,
		interactionSource = interactionSource,
		modifier = modifier
			.padding(21.dp)
			.size(59.dp)
			.background(backgroundColor, RoundedCornerShape(5.dp))
			.border(1.dp, Color.White, RoundedCornerShape(5.dp)),
	) {
		Icon(
			painter = painterResource(id = R.drawable.ic_back),
			contentDescription = "Back",
			tint = Color.White,
		)
	}
}

@Composable
fun CameraGridItem(
	camera: CameraInfo,
	bitmap: Bitmap?,
	isSelected: Boolean,
	onCameraClick: () -> Unit,
	onFocusClick: () -> Unit,
	onZoomClick: () -> Unit,
	modifier: Modifier = Modifier,
) {
	Box(
		modifier = modifier
			.aspectRatio(4f / 3f)
			.background(Color.Black)
			.clickable { onCameraClick() },
	) {
		if (bitmap != null) {
			Image(
				bitmap = bitmap.asImageBitmap(),
				contentDescription = camera.cameraName,
				modifier = Modifier.fillMaxSize(),
				contentScale = ContentScale.Crop,
				colorFilter = ColorFilter.tint(
					color = Color.Black.copy(alpha = 0.3f),
					blendMode = BlendMode.SrcOver,
				),
			)
		}

		Text(
			text = camera.cameraName ?: "",
			color = Color.White,
			fontSize = 22.sp,
			fontWeight = FontWeight.Medium,
			maxLines = 1,
			overflow = TextOverflow.Ellipsis,
			textAlign = TextAlign.Center,
			modifier = Modifier.align(Alignment.Center),
		)

		Box(
			contentAlignment = Alignment.Center,
			modifier = Modifier
				.align(Alignment.TopEnd)
				.size(48.dp)
				.padding(6.dp)
				.clickable(onClick = onZoomClick),
		) {
			Icon(
				painter = painterResource(id = R.drawable.ic_window_control),
				contentDescription = "Window Control",
				tint = Color.White,
			)
		}

		Box(
			modifier = Modifier
				.align(Alignment.BottomCenter)
				.size(40.dp)
				.clickable { onFocusClick() },
			contentAlignment = Alignment.Center,
		) {
			RadioButton(
				selected = isSelected,
				onClick = onFocusClick,
				colors = RadioButtonDefaults.colors(
					selectedColor = Color(0xFF3FC07E),
					unselectedColor = Color.White,
				),
				modifier = Modifier.size(16.dp),
			)
		}
	}
}
