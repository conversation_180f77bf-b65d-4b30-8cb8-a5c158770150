package com.fjdynamics.towermanager.ui

import android.annotation.SuppressLint
import android.content.pm.ActivityInfo
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import android.widget.TextView
import androidx.activity.addCallback
import androidx.annotation.StringRes
import androidx.appcompat.app.AppCompatActivity
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.CollectionUtils
import com.blankj.utilcode.util.StringUtils
import com.blankj.utilcode.util.ThreadUtils
import com.fjdynamics.towermanager.R
import com.fjdynamics.towermanager.util.ActivityManagerUtils
import com.fjdynamics.towermanager.util.LightApiHelper
import com.fjdynamics.towermanager.util.LightApiHelper.getCameraList
import com.fjdynamics.towermanager.widget.dialog.CommonDialog

abstract class BaseFullscreenActivity : AppCompatActivity() {
	private var loadingDialog: CommonDialog? = null

	override fun onCreate(savedInstanceState: Bundle?) {
		super.onCreate(savedInstanceState)
		onBackPressedDispatcher.addCallback {
			//空实现以屏蔽导航栏返回
		}
		window.run {
			setBackgroundDrawable(null)
			setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
		}
		@SuppressLint("SourceLockedOrientationActivity")
		requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE

		BarUtils.setStatusBarVisibility(this, false)
		BarUtils.setNavBarVisibility(this, false)

		ActivityManagerUtils.addActivity(this)
	}

	override fun onResume() {
		super.onResume()
		BarUtils.setStatusBarVisibility(this, false)
		BarUtils.setNavBarVisibility(this, false)
	}

	override fun onDestroy() {
		super.onDestroy()
		ActivityManagerUtils.removeActivity(this)
	}

	protected fun updateFocusHint(tvFocus: TextView?) {
		if (tvFocus == null) {
			return
		}
		val selectedIndex = LightApiHelper.getSelectedIndex()
		val cameraList = getCameraList()
		if (selectedIndex < 0 || selectedIndex >= CollectionUtils.size(cameraList)) {
			tvFocus.visibility = View.GONE
			return
		}
		tvFocus.text = StringUtils.format("Focus：%s", cameraList[selectedIndex].cameraName)
		tvFocus.visibility = View.VISIBLE
	}

	protected fun showLoading() {
		showLoading(StringUtils.getString(R.string.tip_loading_info))
	}

	protected fun showLoading(@StringRes msgResId: Int) {
		showLoading(StringUtils.getString(msgResId))
	}

	protected fun showLoading(msg: String?) {
		ThreadUtils.runOnUiThread {
			if (loadingDialog == null) {
				loadingDialog =
					CommonDialog.showDialog(this, R.layout.dialog_load_view, R.style.dialog)
						.setOnTouchOutside(false)
			}
			val textView = loadingDialog!!.getView<TextView>(R.id.txt_info)
			textView.text = msg
			if (!loadingDialog!!.isShowing) {
				loadingDialog!!.show()
			}
		}
	}

	protected fun hideLoading() {
		ThreadUtils.runOnUiThread {
			if (loadingDialog != null && loadingDialog!!.isShowing) {
				loadingDialog!!.dismiss()
			}
		}
	}
}
