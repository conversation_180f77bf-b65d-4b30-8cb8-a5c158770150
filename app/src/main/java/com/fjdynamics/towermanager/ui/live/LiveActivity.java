package com.fjdynamics.towermanager.ui.live;

import android.graphics.Color;
import android.graphics.drawable.ClipDrawable;
import android.graphics.drawable.ColorDrawable;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.ColorRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.StringRes;

import com.alexvas.rtsp.widget.RtspStatusListener;
import com.alexvas.rtsp.widget.RtspSurfaceView;
import com.blankj.utilcode.util.ClickUtils;
import com.blankj.utilcode.util.GsonUtils;
import com.fjdynamics.towermanager.R;
import com.fjdynamics.towermanager.databinding.ActivityLiveBinding;
import com.fjdynamics.towermanager.entity.BatteryInfo;
import com.fjdynamics.towermanager.entity.TowerData;
import com.fjdynamics.towermanager.event.MessageEvent;
import com.fjdynamics.towermanager.network.model.CameraInfo;
import com.fjdynamics.towermanager.ui.BaseActivity;
import com.fjdynamics.towermanager.ui.cameralist.CameraListActivity;
import com.fjdynamics.towermanager.util.ExecutorUtils;
import com.fjdynamics.towermanager.util.ResourcesUtil;
import com.fjdynamics.towermanager.util.RsaUtils;
import com.fjdynamics.towermanager.websocket.LargeScreenWebSocketManager;
import com.fjdynamics.towermanager.websocket.dto.control.WebSocketMessage;
import com.fjdynamics.towermanager.widget.RoundMenuView;
import com.fjdynamics.towermanager.widget.dialog.CommonDialog;
import com.google.gson.reflect.TypeToken;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;

/**
 * 视觉辅助系统首页
 *
 * <AUTHOR>
 */
public class LiveActivity extends BaseActivity<ActivityLiveBinding> implements View.OnClickListener, RoundMenuView.ChangeStateListener, View.OnTouchListener, Handler.Callback {
	private static final String TAG = "LiveActivity";
	private final Logger logger = LoggerFactory.getLogger(TAG);
	public static final String KEY_CAMERA_INDEX = "KEY_CAMERA_INDEX";
	private static final int MSG_HIDE_OR_SHOW_WIDGET = 101;
	private static final int MSG_RETRY_PREVIEW = 102;
	private final Handler mHandler = new Handler(Looper.getMainLooper(), this);
	private CameraInfo currentCameraInfo;
	// 是否支持云平台 桶机不支持云平台  球机支持云平台
	private boolean isbPtzSupported = false;
	private CommonDialog commonErrorDialog;
	/**
	 * 是否确认过异常，确认过之后不再弹出弹窗
	 */
	private boolean hasConfirmed;
	private ArrayList<CameraInfo> cameraInfoList;

	@Override
	protected ActivityLiveBinding getViewBinding() {
		return ActivityLiveBinding.inflate(getLayoutInflater());
	}

	@Override
	protected void setup(@Nullable Bundle savedInstanceState) {
		initView();
		initCamera();

		sendMessageOrHideWidget(true);
		EventBus.getDefault().register(this);
	}

	/**
	 * @noinspection unchecked
	 */
	private void initCamera() {
		cameraInfoList = (ArrayList<CameraInfo>) getIntent().getSerializableExtra(CameraListActivity.KEY_CAMERA_LIST);
		int clickedIdx = getIntent().getIntExtra(KEY_CAMERA_INDEX, 0);
//		binding.llCamera.setVisibility(View.VISIBLE);
		CameraInfo cameraInfo = getCameraInfo(clickedIdx);
		cameraInfo.setSelect(true);
		currentCameraInfo = cameraInfo;
		isbPtzSupported = cameraInfo.isPtzSupported();
		binding.roundMenuView.setVisibility(isbPtzSupported ? View.VISIBLE : View.GONE);

	}

	/**
	 * 选择上个页面点击的摄像头
	 *
	 * @param idx 索引
	 * @return CameraInfo
	 */
	private CameraInfo getCameraInfo(int idx) {
		for (int i = 0; i < cameraInfoList.size(); i++) {
			cameraInfoList.get(i).setSelect(false);
		}
		return cameraInfoList.get(idx);
	}

	private void sendMessageOrHideWidget(boolean isSendMessage) {
		hideWidget(true);
		if (mHandler.hasMessages(MSG_HIDE_OR_SHOW_WIDGET)) {
			mHandler.removeMessages(MSG_HIDE_OR_SHOW_WIDGET);
		}
		if (isSendMessage) {
			mHandler.sendEmptyMessageDelayed(MSG_HIDE_OR_SHOW_WIDGET, 15_000);
		}
	}

	private void initView() {
		binding.getRoot().setOnClickListener(v -> sendMessageOrHideWidget(true));
		binding.roundMenuView.setChangeStateListener(this);
		binding.llMinus.setOnTouchListener(this);
		binding.llAdd.setOnTouchListener(this);

		View[] views = new View[2];
		views[0] = binding.llBack;
		views[1] = binding.llCamera;
		ClickUtils.applySingleDebouncing(views, 1_000, this);

		RtspStatusListener rtspStatusListener = new RtspStatusListener() {

			@Override
			public void onRtspFrameSizeChanged(int width, int height) {
				logger.info("onRtspFrameSizeChanged: w -> {}, h -> {}", width, height);
				hideLoading();
				hideErrorDialog();
			}

			@Override
			public void onRtspFirstFrameRendered() {
				logger.info("onRtspFirstFrameRendered");
			}

			@Override
			public void onRtspStatusFailed(@Nullable String s) {
				logger.error("onRtspStatusFailed: {}", s);
				hideLoading();
				showErrorDialog(R.string.tip_camera_play_error);
				mHandler.sendEmptyMessageDelayed(MSG_RETRY_PREVIEW, 5_000);
			}

			@Override
			public void onRtspStatusFailedUnauthorized() {
				logger.error("onRtspStatusFailedUnauthorized");
				hideLoading();
				showErrorDialog(R.string.connecting_to_camera_failed);
			}

			@Override
			public void onRtspStatusDisconnected() {
				logger.info("onRtspStatusDisconnected");
			}

			@Override
			public void onRtspStatusDisconnecting() {
				logger.info("onRtspStatusDisconnecting");
			}

			@Override
			public void onRtspStatusConnected() {
				logger.info("onRtspStatusConnected");
			}

			@Override
			public void onRtspStatusConnecting() {
				logger.info("onRtspStatusConnecting");
			}
		};
		binding.svCamera.setStatusListener(rtspStatusListener);
	}

	@Override
	public boolean onTouch(@NonNull View view, @NonNull MotionEvent event) {
		int action = event.getAction();
		int viewId = view.getId();

		// 只处理指定的View
		if (viewId != R.id.ll_minus && viewId != R.id.ll_add) {
			return false;
		}

		sendMessageOrHideWidget(action == MotionEvent.ACTION_UP);

		if (viewId == R.id.ll_minus) {
			onPtzCtrlFocus(action, 0x0304);
		} else if (viewId == R.id.ll_add) {
			onPtzCtrlFocus(action, 0x0302);
		}

		// 只对当前被触摸的View应用背景色变化
		if (action == MotionEvent.ACTION_DOWN) {
			view.setBackgroundColor(Color.parseColor("#CC000000"));
		} else if (action == MotionEvent.ACTION_UP || action == MotionEvent.ACTION_CANCEL) {
			view.setBackgroundColor(Color.parseColor("#CC191923"));
		}

		return true;
	}

	private void onPtzCtrlFocus(int action, int para) {
		if (action == MotionEvent.ACTION_DOWN) {
			LargeScreenWebSocketManager.getInstance().sendCameraControl(para, currentCameraInfo);
		} else if (action == MotionEvent.ACTION_UP) {
			LargeScreenWebSocketManager.getInstance().sendCameraControl(0x0901, currentCameraInfo);
		}
	}

	@Override
	public void onChangeState(boolean isDown, RoundMenuView.AreaCode area) {
		int cmd = 0;
		int dwSpeed;
		sendMessageOrHideWidget(!isDown);
		if (isDown) {
			dwSpeed = 2;
			switch (area) {
				case NUMBER_ONE:
					cmd = 0x0402; // up
					break;
				case NUMBER_TWO:
					cmd = 0x0802; // right_up
					break;
				case NUMBER_THREE:
					cmd = 0x0502; // right
					break;
				case NUMBER_FOUR:
					cmd = 0x0804; // right_down
					break;
				case NUMBER_FIVE:
					cmd = 0x0404; // down
					break;
				case NUMBER_SIX:
					cmd = 0x0704; // left_down
					break;
				case NUMBER_SEVEN:
					cmd = 0x0504; // left
					break;
				case NUMBER_EIGHT:
					cmd = 0x0702; // left_up
					break;
				default:
					break;
			}
		} else {
			dwSpeed = 6;
			cmd = 0x0901;
		}
		sendControl(cmd, dwSpeed);
	}

	private void sendControl(int dwPTZCommand, int dwSpeed) {
		LargeScreenWebSocketManager.getInstance().sendCameraControl(dwPTZCommand, currentCameraInfo);
	}

	@Override
	public void onClick(@NonNull View view) {
		int id = view.getId();
		if (id == R.id.ll_camera) {
			if (mHandler.hasMessages(MSG_HIDE_OR_SHOW_WIDGET)) {
				mHandler.removeMessages(MSG_HIDE_OR_SHOW_WIDGET);
			}
			handleScreenZoom();
		} else if (id == R.id.ll_back) {
			finish();
		}
	}

	private void handleScreenZoom() {
		LargeScreenWebSocketManager.getInstance().sendWebWindowZoomCommand(currentCameraInfo.getPosType());
	}

	private void startPreview(@NonNull CameraInfo cameraInfo) {
		RtspSurfaceView rtspSurfaceView = binding.svCamera;
		rtspSurfaceView.init(
			Uri.parse("rtsp://" + cameraInfo.getIntranetIp() + ":554/media/video2"),
			cameraInfo.getLoginName(),
			RsaUtils.decrypt(cameraInfo.getPassword()),
			""
		);
		rtspSurfaceView.start(true, false, false);
	}

	@Override
	protected void onResume() {
		super.onResume();
		showLoading(R.string.connecting_to_camera);
		startPreview(currentCameraInfo);
	}

	@Override
	protected void onPause() {
		super.onPause();
		if (binding.svCamera.isStarted()) {
			binding.svCamera.stop();
		}
	}

	@Override
	protected void onDestroy() {
		EventBus.getDefault().unregister(this);
		mHandler.removeCallbacksAndMessages(null);
		super.onDestroy();
	}

	private void hideWidget(boolean isShow) {
		binding.llBack.setVisibility(isShow ? View.VISIBLE : View.GONE);
		binding.roundMenuView.setVisibility(isbPtzSupported ? isShow ? View.VISIBLE : View.GONE : View.GONE);
		binding.llMinus.setVisibility(isShow ? View.VISIBLE : View.GONE);
		binding.llCamera.setVisibility(isShow ? View.VISIBLE : View.GONE);
//		binding.llCamera.setVisibility(isShowCamera ? isShow ? View.VISIBLE : View.GONE : View.GONE);
		binding.llAdd.setVisibility(isShow ? View.VISIBLE : View.GONE);
	}

	@Override
	public boolean handleMessage(@NonNull Message msg) {
		if (msg.what == MSG_HIDE_OR_SHOW_WIDGET) {
			hideWidget(false);
			return true;
		} else if (msg.what == MSG_RETRY_PREVIEW) {
			startPreview(currentCameraInfo);
			return true;
		}
		return false;
	}

	/**
	 * 收到1904的消息
	 *
	 * @param event event
	 */
	@Subscribe(threadMode = ThreadMode.MAIN)
	public void onMessageEvent(@NonNull MessageEvent event) {
		if (event.getCode().equals(MessageEvent.CODE_1904_MESSAGE_RECEIVED)) {
			ExecutorUtils.getInstance().execute(() -> {
				Object data = event.getData();
				if (!(data instanceof String json)) {
					return;
				}
				try {
					JSONObject jo = new JSONObject(json);
					String msgType = jo.optString("messageType");
					if (TextUtils.equals(msgType, WebSocketMessage.TOWER_DATA)) {
						WebSocketMessage<TowerData> wsMsg = GsonUtils.fromJson(json, new TypeToken<WebSocketMessage<TowerData>>() {
						}.getType());
						TowerData towerData = wsMsg.getData();
						if (towerData == null) {
							return;
						}
						Double batterySoc = towerData.getBatterySoc();
						Integer batteryStatus = towerData.getBatteryStatus();
						if (batterySoc == null || batteryStatus == null) {
							handleBatteryUpdate(null);
							return;
						}
						handleBatteryUpdate(new BatteryInfo(batterySoc, batteryStatus == 1));
					}
				} catch (Exception e) {
					logger.error("control msg parse exception: {}", e.getMessage());
				}
			});
		}
	}

	private void handleBatteryUpdate(BatteryInfo batteryInfo) {
		if (batteryInfo == null) {
			logger.warn("handleBatteryUpdate: batteryInfo is null");
			binding.conBattery.setVisibility(View.GONE);
			return;
		}
		binding.conBattery.setVisibility(View.VISIBLE);
		int percent = (int) batteryInfo.getPercent();
		binding.ivBattery.setVisibility(batteryInfo.isCharging() ? View.VISIBLE : View.GONE);
		binding.llBattery.setVisibility(batteryInfo.isCharging() ? View.GONE : View.VISIBLE);
		setProgressBarBg(percent < 20 ? R.color.color_FFFF4B50 : R.color.color_FF28CD78, percent);
		binding.tvBattery.setText(String.format("%s%%", percent));
	}

	private void setProgressBarBg(@ColorRes int color, int progress) {
		ClipDrawable drawable = new ClipDrawable(new ColorDrawable(ResourcesUtil.getColor(this, color)), Gravity.START, ClipDrawable.HORIZONTAL);
		binding.progressBar.setProgressDrawable(drawable);
		drawable.setLevel(progress * 100);
		binding.progressBar.setProgressDrawable(drawable);
		binding.progressBar.setProgress(progress);
	}

	private void showErrorDialog(@StringRes int titleId) {
		if (commonErrorDialog == null) {
			commonErrorDialog = CommonDialog.showDialog(this, R.layout.dialog_play_error_view, R.style.dialog).setOnTouchOutside(false).setViewListener((dialog, view) -> {
				hasConfirmed = true;
				dialog.dismiss();
			}, R.id.tv_quit);
		}
		TextView tvTipTitle = commonErrorDialog.getView(R.id.tv_tip_title);
		tvTipTitle.setText(getString(titleId));
		if (!commonErrorDialog.isShowing() && !hasConfirmed) {
			commonErrorDialog.show();
		}
	}

	private void hideErrorDialog() {
		if (commonErrorDialog == null || !commonErrorDialog.isShowing()) {
			return;
		}
		commonErrorDialog.dismiss();
		commonErrorDialog = null;
	}

	@Override
	public void onBackPressed() {
		// 禁用返回键
	}

	/**
	 * 获取当前被选中的摄像头信息
	 *
	 * @return cameraInfo
	 */
	private CameraInfo getSelectedCamera() {
		for (CameraInfo cameraInfo : cameraInfoList) {
			if (cameraInfo.isSelect()) {
				return cameraInfo;
			}
		}
		return null;
	}
}
