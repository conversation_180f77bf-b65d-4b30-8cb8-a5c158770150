package com.fjdynamics.towermanager.ui.home

import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Bundle
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.view.ViewConfiguration
import android.view.WindowManager
import android.widget.Toast
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.blankj.utilcode.util.ScreenUtils
import com.blankj.utilcode.util.SizeUtils
import com.blankj.utilcode.util.ToastUtils
import com.fjdynamics.towermanager.R
import com.fjdynamics.towermanager.databinding.ActivityHomeBinding
import com.fjdynamics.towermanager.ui.BaseBindingActivity
import com.fjdynamics.towermanager.ui.cameralist.CameraListActivity
import com.fjdynamics.towermanager.ui.intercom.IntercomActivity
import com.fjdynamics.towermanager.ui.login.LoginActivity
import com.fjdynamics.towermanager.ui.maintenance.MaintenanceActivity
import com.fjdynamics.towermanager.ui.personal.PersonalActivity
import com.fjdynamics.towermanager.ui.task.TaskActivity
import com.fjdynamics.towermanager.util.ActivityManagerUtils
import com.fjdynamics.towermanager.util.LightApiHelper
import com.fjdynamics.towermanager.widget.dialog.WarnDialog
import kotlin.math.sqrt
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.viewModel


/**
 * 司机中控平板主界面
 *
 * <AUTHOR>
 * @since 2025/4/25
 */
class HomeActivity : BaseBindingActivity<ActivityHomeBinding>() {

	private val viewModel: HomeViewModel by viewModel()
	private var shutdownWarnDialog: WarnDialog? = null

	private var floatingTimeRecorderView: View? = null

	override fun onCreate(savedInstanceState: Bundle?) {
		super.onCreate(savedInstanceState)

		initViewClickListener()

		initFlowCollector()

		viewModel.fetchData()

		addFloatingTimeRecorderView()
	}

	private fun initFlowCollector() {
		lifecycleScope.launch {
			repeatOnLifecycle(Lifecycle.State.CREATED) {
				launch {
					viewModel.homeUiState.collect { uiState ->
						binding.tvDriver.text = uiState.currUserInfo?.name

						binding.tvProject.text = uiState.currProjectInfo?.projectName

						uiState.currTowerInfo?.let {
							binding.tvTower.text = if (it.model.isNullOrEmpty()) {
								it.towerName
							} else {
								"${it.towerName} / ${it.model}"
							}
						}

						if (uiState.userLogout) {
							gotoLoginPage()
						}
					}
				}

				launch {
					viewModel.loadingUiState.collect { loadingUiState ->
						if (loadingUiState.loading) {
							if (loadingUiState.msg.isEmpty()) {
								showLoading()
							} else {
								showLoading(loadingUiState.msg)
							}
						} else {
							hideLoading()
						}
					}
				}

				launch {
					viewModel.toastUiState.collect { toastUiState ->
						if (toastUiState.msg.isNotEmpty()) {
							if (toastUiState.duration == Toast.LENGTH_SHORT) {
								ToastUtils.showShort(toastUiState.msg)
							} else {
								ToastUtils.showLong(toastUiState.msg)
							}
						}
					}
				}

				launch {
					LightApiHelper.cameraFocusUpdateFlow.collect {
						updateFocusHint(binding.tvFocusHint)
					}
				}

			}
		}
	}

	private fun initViewClickListener() {
		binding.cardDriver.setOnClickListener {
			startActivity(Intent(this, PersonalActivity::class.java))
		}
		binding.cardChannel.setOnClickListener {
			startActivity(
				Intent(
					this,
					IntercomActivity::class.java,
				).also { it.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT) },
			)
		}
		binding.btnTask.setOnClickListener {
			startActivity(
				Intent(
					this,
					TaskActivity::class.java,
				).also { it.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT) },
			)
		}
		binding.btnMaintenance.setOnClickListener {
			startActivity(Intent(this, MaintenanceActivity::class.java))
		}
		binding.ivShutdown.setOnClickListener {
			if (shutdownWarnDialog == null) {
				shutdownWarnDialog = WarnDialog(this) { confirmed ->
					if (!confirmed) {
						return@WarnDialog
					}
					viewModel.sendShutDownCommand()
				}
			}
			shutdownWarnDialog?.let {
				if (!it.isShowing) {
					it.show("确认现在关机？")
				}
			}
		}
		binding.btnCamera.setOnClickListener {
			viewModel.getLatestCameraList { cameraList ->
				val intent = Intent(this, CameraListActivity::class.java)
				intent.putExtra(CameraListActivity.KEY_CAMERA_LIST, ArrayList(cameraList))
				startActivity(intent)
			}
		}
	}

	private fun gotoLoginPage() {
		val intent = Intent(this, LoginActivity::class.java)
		startActivity(intent)
		ActivityManagerUtils.finishAll()
	}

	@SuppressLint("InflateParams")
	private fun addFloatingTimeRecorderView() {
		if (floatingTimeRecorderView?.isShown == true) {
			return
		}
		if (floatingTimeRecorderView == null) {
			floatingTimeRecorderView =
				layoutInflater.inflate(R.layout.layout_floating_record_button, null)
		}
		val params = WindowManager.LayoutParams(
			SizeUtils.dp2px(80f),
			SizeUtils.dp2px(80f),
			WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
			WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
			PixelFormat.TRANSLUCENT,
		).apply {
			gravity = Gravity.TOP or Gravity.START
			x = ScreenUtils.getAppScreenWidth() - (SizeUtils.dp2px(80f) + SizeUtils.dp2px(20f))
			y = SizeUtils.dp2px(70f)
		}
		windowManager.addView(floatingTimeRecorderView, params)

		floatingTimeRecorderView?.setOnTouchListener(
			object : View.OnTouchListener {
				private var initialX = 0f
				private var initialY = 0f
				private var initialTouchX = 0f
				private var initialTouchY = 0f
				private val touchSlop = ViewConfiguration.get(this@HomeActivity).scaledTouchSlop
				private var isDragging = false

				override fun onTouch(v: View, event: MotionEvent): Boolean {
					when (event.action) {
						MotionEvent.ACTION_DOWN -> {
							isDragging = false
							initialX = params.x.toFloat()
							initialY = params.y.toFloat()
							initialTouchX = event.rawX
							initialTouchY = event.rawY
							return true
						}

						MotionEvent.ACTION_MOVE -> {
							val dx = event.rawX - initialTouchX
							val dy = event.rawY - initialTouchY

							if (!isDragging && sqrt((dx * dx + dy * dy).toDouble()) > touchSlop) {
								isDragging = true
							}

							if (isDragging) {
								params.x = (initialX + dx).toInt()
								params.y = (initialY + dy).toInt()
								windowManager.updateViewLayout(floatingTimeRecorderView, params)
							}
							return true
						}

						MotionEvent.ACTION_UP -> {
							if (!isDragging) {
								v.performClick()
							}
							return true
						}
					}
					return false
				}
			},
		)

		floatingTimeRecorderView?.setOnClickListener {
			viewModel.saveCurrentTimestampToDb()
			ToastUtils.showShort("异常时间记录成功")
		}
	}

	private fun removeFloatingTimeRecorderView() {
		if (floatingTimeRecorderView != null) {
			windowManager.removeView(floatingTimeRecorderView)
		}
	}

	override fun onDestroy() {
		super.onDestroy()
		removeFloatingTimeRecorderView()
	}
}
