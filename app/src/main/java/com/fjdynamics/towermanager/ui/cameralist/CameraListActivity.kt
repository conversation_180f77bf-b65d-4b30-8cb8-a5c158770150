package com.fjdynamics.towermanager.ui.cameralist

import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import com.fjdynamics.towermanager.network.model.CameraInfo
import com.fjdynamics.towermanager.ui.BaseFullscreenActivity
import com.fjdynamics.towermanager.ui.live.LiveActivity
import com.fjdynamics.towermanager.ui.theme.TowerCranePadTheme
import org.koin.androidx.viewmodel.ext.android.viewModel

/**
 * 视频墙页面
 *
 * <AUTHOR>
 */
class CameraListActivity : BaseFullscreenActivity() {

	companion object {
		const val KEY_CAMERA_LIST = "KEY_CAMERA_LIST"
	}

	private val viewModel: CameraListViewModel by viewModel()

	@Suppress("UNCHECKED_CAST", "DEPRECATION")
	override fun onCreate(savedInstanceState: Bundle?) {
		super.onCreate(savedInstanceState)

		// 获取摄像头列表
		val cameraList = intent.getSerializableExtra(KEY_CAMERA_LIST) as? ArrayList<CameraInfo>
		if (cameraList.isNullOrEmpty()) {
			finish()
			return
		}

		viewModel.initCameraList(cameraList)

		setContent {
			TowerCranePadTheme {
				Surface(
					modifier = Modifier.fillMaxSize(),
					color = MaterialTheme.colorScheme.background,
				) {
					CameraListScreen(
						viewModel = viewModel,
						onBackClick = { finish() },
						onCameraClick = { index ->
							Intent(this@CameraListActivity, LiveActivity::class.java)
								.putExtra(KEY_CAMERA_LIST, cameraList)
								.putExtra(LiveActivity.KEY_CAMERA_INDEX, index)
								.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
								.also { startActivity(it) }
						},
					)
				}
			}
		}
	}

	override fun onResume() {
		super.onResume()
		viewModel.startImageLoading()
	}

	override fun onPause() {
		super.onPause()
		viewModel.stopImageLoading()
	}

}
