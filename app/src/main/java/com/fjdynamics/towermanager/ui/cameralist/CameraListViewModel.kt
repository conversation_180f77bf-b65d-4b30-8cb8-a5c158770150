package com.fjdynamics.towermanager.ui.cameralist

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import androidx.lifecycle.viewModelScope
import com.fjdynamics.towermanager.event.MessageEvent
import com.fjdynamics.towermanager.network.model.CameraInfo
import com.fjdynamics.towermanager.network.service.LightApiService
import com.fjdynamics.towermanager.ui.BaseViewModel
import com.fjdynamics.towermanager.util.Analytics
import com.fjdynamics.towermanager.util.AnalyticsEvent
import com.fjdynamics.towermanager.util.BitmapUtils
import com.fjdynamics.towermanager.util.LightApiHelper
import com.fjdynamics.towermanager.util.recordEvent
import com.fjdynamics.towermanager.websocket.LargeScreenWebSocketManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus

/**
 * 视频墙页面ViewModel
 *
 * <AUTHOR>
 */
class CameraListViewModel(private val lightApiService: LightApiService) : BaseViewModel() {
	private val _uiState = MutableStateFlow(CameraListUiState())
	val uiState: StateFlow<CameraListUiState> = _uiState.asStateFlow()
	private val imageLoadJobs = mutableMapOf<String, Job>()

	fun initCameraList(cameraList: List<CameraInfo>) {
		_uiState.value = _uiState.value.copy(
			cameraList = cameraList,
			selectedIndex = LightApiHelper.getSelectedIndex(),
		)
	}

	fun focusCamera(index: Int) {
		val currentState = _uiState.value
		if (index >= currentState.cameraList.size) return

		_uiState.value = currentState.copy(selectedIndex = index)

		LightApiHelper.updateSelectedIndex(index)
		EventBus.getDefault().post(MessageEvent(MessageEvent.CODE_CAMERA_FOCUS_CHANGED, null))

		val cameraInfo = currentState.cameraList[index]
		Analytics.recordEvent(AnalyticsEvent.CameraFocus) {
			put("camera_name", cameraInfo.cameraName)
			put("camera_ip", cameraInfo.intranetIp)
		}
	}

	fun zoomCamera(index: Int) {
		val currentState = _uiState.value
		if (index >= currentState.cameraList.size) return

		val cameraInfo = currentState.cameraList[index]
		LargeScreenWebSocketManager.getInstance().sendWebWindowZoomCommand(cameraInfo.posType)
	}

	fun onCameraClick(index: Int) {
		val currentState = _uiState.value
		if (index >= currentState.cameraList.size) return

		val cameraInfo = currentState.cameraList[index]
		Analytics.recordEvent(AnalyticsEvent.EnterStreaming) {
			put("camera_name", cameraInfo.cameraName)
			put("camera_ip", cameraInfo.intranetIp)
		}
	}

	fun startImageLoading() {
		val currentState = _uiState.value
		currentState.cameraList.forEachIndexed { index, cameraInfo ->
			loadImage(cameraInfo.intranetIp, index)
		}
	}

	fun stopImageLoading() {
		imageLoadJobs.values.forEach { it.cancel() }
	}

	private fun loadImage(ip: String, index: Int) {
		imageLoadJobs[ip]?.cancel()
		imageLoadJobs[ip] = viewModelScope.launch {
			val url = "http://$ip/LAPI/V1.0/Channels/0/Media/Video/Streams/0/Snapshot"
			val bitmap = getPreviewBitmap(url)
			if (bitmap != null) {
				updateCameraImage(index, bitmap)
			} else {
				// 3秒后重试
				delay(3_000L)
				if (isActive) {
					loadImage(ip, index)
				}
			}
		}
	}

	private suspend fun getPreviewBitmap(url: String): Bitmap? = withContext(Dispatchers.IO) {
		try {
			val call = lightApiService.getPreviewImage(url)
			val response = call.execute()

			if (!response.isSuccessful) {
				logger.debug("getPreviewBitmap response not successful: $url")
				return@withContext null
			}

			response.body()?.use { body ->
				val imageData = body.bytes()
				val options = BitmapFactory.Options().apply {
					inJustDecodeBounds = true
				}
				BitmapFactory.decodeByteArray(imageData, 0, imageData.size, options)

				options.inSampleSize =
					BitmapUtils.calculateInSampleSize(options.outWidth, options.outHeight)
				options.inJustDecodeBounds = false

				logger.debug("getPreviewBitmap from: $url, original size: ${options.outWidth}x${options.outHeight}, inSampleSize: ${options.inSampleSize}")

				BitmapFactory.decodeByteArray(imageData, 0, imageData.size, options)
			}
		} catch (e: Exception) {
			logger.error("getPreviewBitmap exception: ${e.message}")
			null
		}
	}

	private fun updateCameraImage(index: Int, bitmap: Bitmap) {
		val currentState = _uiState.value
		val updatedImages = currentState.cameraImages.toMutableMap()
		updatedImages[index] = bitmap

		_uiState.value = currentState.copy(cameraImages = updatedImages)
	}

	override fun onCleared() {
		super.onCleared()
		stopImageLoading()
		imageLoadJobs.clear()
	}
}
