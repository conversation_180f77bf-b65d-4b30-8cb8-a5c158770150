package com.fjdynamics.towermanager.di

import com.fjdynamics.towermanager.ui.cameralist.CameraListViewModel
import com.fjdynamics.towermanager.ui.home.HomeViewModel
import com.fjdynamics.towermanager.ui.maintenance.anomaly.AnomalyTimeRecordViewModel
import org.koin.core.module.dsl.viewModelOf
import org.koin.dsl.module

fun viewModelModules() = module {
	viewModelOf(::HomeViewModel)
	viewModelOf(::AnomalyTimeRecordViewModel)
	viewModelOf(::CameraListViewModel)
}
