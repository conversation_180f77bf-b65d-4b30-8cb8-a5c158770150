package com.fjdynamics.towermanager.widget;

import android.graphics.Matrix;
import android.graphics.PointF;
import android.graphics.Rect;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;

import java.util.ArrayList;
import java.util.List;

/**
 * 视频手势控制器，支持双指缩放和单指拖拽
 * 参考CPlayView的实现
 *
 * <AUTHOR>
 */
public class VideoGestureController implements View.OnTouchListener {

    /**
     * 记录是拖拽模式还是缩放模式
     */
    private int mode = MODE_NONE;

    /**
     * 初始状态
     */
    private static final int MODE_NONE = 0;
    /**
     * 拖拽模式
     */
    private static final int MODE_DRAG = 1;
    /**
     * 缩放模式
     */
    private static final int MODE_ZOOM = 2;

    /**
     * 当前缩放倍数
     */
    private float currentScale = 1.0f;
    /**
     * 上一次缩放倍数
     */
    private float lastScale = 1.0f;
    /**
     * 最小缩放倍数
     */
    private static final float MIN_SCALE = 1.0f;
    /**
     * 最大缩放倍数
     */
    private static final float MAX_SCALE = 10.0f;

    /**
     * 初始两指距离
     */
    private float originalDistance;
    /**
     * 当前两指距离
     */
    private float currentDistance;

    /**
     * 上次触摸位置
     */
    private PointF lastTouchPoint = new PointF();
    /**
     * 缩放中心点
     */
    private PointF scaleCenter = new PointF();

    /**
     * 变换矩阵
     */
    private Matrix matrix = new Matrix();
    private Matrix savedMatrix = new Matrix();

    /**
     * 目标View
     */
    private View targetView;

    /**
     * 手势监听器
     */
    private OnGestureListener gestureListener;

    /**
     * 排除的UI控件列表，这些控件区域内的触摸不会触发手势
     */
    private List<View> excludedViews = new ArrayList<>();

    /**
     * 双击检测相关
     */
    private long lastTapTime = 0;
    private static final long DOUBLE_TAP_TIMEOUT = 300; // 双击间隔时间

    public VideoGestureController(View targetView) {
        this.targetView = targetView;
        targetView.setOnTouchListener(this);
    }

    @Override
    public boolean onTouch(View view, MotionEvent event) {
        // 检查触摸点是否在排除的UI控件区域内
        if (isTouchInExcludedArea(event)) {
            return false; // 不处理手势，让其他控件处理
        }

        switch (event.getAction() & MotionEvent.ACTION_MASK) {
            case MotionEvent.ACTION_DOWN:
                // 单指按下，进入拖拽模式
                mode = MODE_DRAG;
                savedMatrix.set(matrix);
                lastTouchPoint.set(event.getX(), event.getY());
                break;

            case MotionEvent.ACTION_POINTER_DOWN:
                // 双指按下，进入缩放模式
                mode = MODE_ZOOM;
                originalDistance = getDistance(event);
                if (originalDistance > 10f) {
                    savedMatrix.set(matrix);
                    getMiddlePoint(scaleCenter, event);
                }
                break;

            case MotionEvent.ACTION_MOVE:
                if (mode == MODE_DRAG) {
                    // 拖拽模式
                    if (currentScale > MIN_SCALE) {
                        matrix.set(savedMatrix);
                        float dx = event.getX() - lastTouchPoint.x;
                        float dy = event.getY() - lastTouchPoint.y;
                        matrix.postTranslate(dx, dy);

                        // 边界检测
                        limitTranslation();

                        if (gestureListener != null) {
                            gestureListener.onTranslate(dx, dy, matrix);
                        }
                    }
                } else if (mode == MODE_ZOOM) {
                    // 缩放模式
                    currentDistance = getDistance(event);
                    if (currentDistance > 10f && Math.abs(currentDistance - originalDistance) > 10f) {
                        matrix.set(savedMatrix);
                        float scale = currentDistance / originalDistance;

                        // 计算新的缩放倍数
                        float newScale = lastScale * scale;

                        // 限制缩放范围
                        if (newScale < MIN_SCALE) {
                            scale = MIN_SCALE / lastScale;
                            newScale = MIN_SCALE;
                        } else if (newScale > MAX_SCALE) {
                            scale = MAX_SCALE / lastScale;
                            newScale = MAX_SCALE;
                        }

                        currentScale = newScale;
                        matrix.postScale(scale, scale, scaleCenter.x, scaleCenter.y);

                        if (gestureListener != null) {
                            gestureListener.onScale(currentScale, scaleCenter.x, scaleCenter.y, matrix);
                        }
                    }
                }
                break;

            case MotionEvent.ACTION_UP:
                if (mode == MODE_DRAG) {
                    // 检测双击重置
                    long currentTime = System.currentTimeMillis();
                    if (currentTime - lastTapTime < DOUBLE_TAP_TIMEOUT) {
                        // 双击重置
                        reset();
                    }
                    lastTapTime = currentTime;
                }
                mode = MODE_NONE;
                lastScale = currentScale;
                break;

            case MotionEvent.ACTION_POINTER_UP:
                mode = MODE_NONE;
                lastScale = currentScale;
                break;
        }

        return true;
    }

    /**
     * 计算两点间距离
     */
    private float getDistance(MotionEvent event) {
        if (event.getPointerCount() < 2) {
            return 0;
        }
        float x = event.getX(0) - event.getX(1);
        float y = event.getY(0) - event.getY(1);
        return (float) Math.sqrt(x * x + y * y);
    }

    /**
     * 获取两点中心
     */
    private void getMiddlePoint(PointF point, MotionEvent event) {
        if (event.getPointerCount() < 2) {
            return;
        }
        float x = event.getX(0) + event.getX(1);
        float y = event.getY(0) + event.getY(1);
        point.set(x / 2, y / 2);
    }

    /**
     * 限制平移范围，防止画面移出边界
     */
    private void limitTranslation() {
        if (targetView == null) {
            return;
        }

        float[] values = new float[9];
        matrix.getValues(values);

        float transX = values[Matrix.MTRANS_X];
        float transY = values[Matrix.MTRANS_Y];
        float scaleX = values[Matrix.MSCALE_X];
        float scaleY = values[Matrix.MSCALE_Y];

        int viewWidth = targetView.getWidth();
        int viewHeight = targetView.getHeight();

        float scaledWidth = viewWidth * scaleX;
        float scaledHeight = viewHeight * scaleY;

        // 计算边界
        float maxTransX = 0;
        float minTransX = viewWidth - scaledWidth;
        float maxTransY = 0;
        float minTransY = viewHeight - scaledHeight;

        // 限制X方向平移
        if (scaledWidth <= viewWidth) {
            transX = (viewWidth - scaledWidth) / 2;
        } else {
            if (transX > maxTransX) {
                transX = maxTransX;
            } else if (transX < minTransX) {
                transX = minTransX;
            }
        }

        // 限制Y方向平移
        if (scaledHeight <= viewHeight) {
            transY = (viewHeight - scaledHeight) / 2;
        } else {
            if (transY > maxTransY) {
                transY = maxTransY;
            } else if (transY < minTransY) {
                transY = minTransY;
            }
        }

        // 应用限制后的变换
        values[Matrix.MTRANS_X] = transX;
        values[Matrix.MTRANS_Y] = transY;
        matrix.setValues(values);
    }

    /**
     * 重置缩放和平移
     */
    public void reset() {
        currentScale = MIN_SCALE;
        lastScale = MIN_SCALE;
        matrix.reset();
        if (gestureListener != null) {
            gestureListener.onReset(matrix);
        }
    }

    /**
     * 获取当前缩放倍数
     */
    public float getCurrentScale() {
        return currentScale;
    }

    /**
     * 检查触摸点是否在排除的UI控件区域内
     */
    private boolean isTouchInExcludedArea(MotionEvent event) {
        float x = event.getRawX();
        float y = event.getRawY();

        for (View excludedView : excludedViews) {
            if (excludedView.getVisibility() == View.VISIBLE) {
                Rect rect = new Rect();
                excludedView.getGlobalVisibleRect(rect);
                if (rect.contains((int) x, (int) y)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 添加排除的UI控件
     */
    public void addExcludedView(View view) {
        if (view != null && !excludedViews.contains(view)) {
            excludedViews.add(view);
        }
    }

    /**
     * 移除排除的UI控件
     */
    public void removeExcludedView(View view) {
        excludedViews.remove(view);
    }

    /**
     * 清空所有排除的UI控件
     */
    public void clearExcludedViews() {
        excludedViews.clear();
    }

    /**
     * 设置手势监听器
     */
    public void setOnGestureListener(OnGestureListener listener) {
        this.gestureListener = listener;
    }

    /**
     * 手势监听接口
     */
    public interface OnGestureListener {
        /**
         * 缩放事件
         * @param scale 当前缩放倍数
         * @param focusX 缩放中心X
         * @param focusY 缩放中心Y
         * @param matrix 变换矩阵
         */
        void onScale(float scale, float focusX, float focusY, Matrix matrix);

        /**
         * 平移事件
         * @param dx X方向位移
         * @param dy Y方向位移
         * @param matrix 变换矩阵
         */
        void onTranslate(float dx, float dy, Matrix matrix);

        /**
         * 重置事件
         * @param matrix 变换矩阵
         */
        void onReset(Matrix matrix);
    }
}
