package com.fjdynamics.towermanager.widget;

import android.content.Context;
import android.opengl.GLSurfaceView;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import com.sdk.NetDEVSDK;

import javax.microedition.khronos.egl.EGL10;
import javax.microedition.khronos.egl.EGLConfig;
import javax.microedition.khronos.egl.EGLContext;
import javax.microedition.khronos.egl.EGLDisplay;
import javax.microedition.khronos.opengles.GL10;

public class CPlayView extends GLSurfaceView {
	public boolean m_bCanDrawFrame = true; // false
	private boolean m_bRenderInited = false; // render
	public int m_dwWinIndex = 10; // false

	/**
	 * 记录是拖拉照片模式还是放大缩小照片模式
	 */
	private int mode = 0; // 初始
	/**
	 * 拖拉照片模式
	 */
	private static final int MODE_DRAG = 1;
	/**
	 * 放大缩小照片模式
	 */
	private static final int MODE_ZOOM = 2;
	/**
	 * 当前放大的倍数
	 */
	private float mCurrentRate = 1;
	/**
	 * 上一次放大的倍数
	 */
	private float mOldRate = 1;
	/**
	 * 刚触摸时两个手指的距离
	 */
	private float mOriginalLength;
	/**
	 * 当前两个手指的距离
	 */
	private float mCurrentLength;

	private float scaleEx = 1.0f;
	float xLastMove = 0.0f;
	float yLastMove = 0.0f;
	float fTotal = 0.0f;
	private float[] fCenterPosition;
	private OnTouchChangeListener onTouchChangeListener;
	private final OnTouchListener onTouchListener = new OnTouchListener() {
		@Override
		public boolean onTouch(View view, MotionEvent event) {
			if (onTouchChangeListener != null) {
				onTouchChangeListener.onTouchChange(event);
			}
//			switch (event.getAction() & MotionEvent.ACTION_MASK) {
//				case MotionEvent.ACTION_POINTER_DOWN:
//					mode = MODE_ZOOM;
//					mOriginalLength = (float) Math.sqrt(Math.pow(event.getX(0) - event.getX(1), 2) + Math.pow(event.getY(0) - event.getY(1), 2));
//					// 0.47-0.53有效
//					float percentX = 0.47f + ((event.getX(0) + event.getX(1)) / 2) / (getWidth()) * 0.06f;
//					float percentY = 0.53f - ((event.getY(0) + event.getY(1)) / 2) / (getHeight()) * 0.06f;
//					// 数字放大手指中心点
//					fCenterPosition = new float[]{percentX, percentY};
//					break;
//				// 手指压下屏幕
//				case MotionEvent.ACTION_DOWN:
//					mode = MODE_DRAG;
//					xLastMove = event.getX();
//					yLastMove = event.getY();
//					break;
//				// 当触点离开屏幕，但是屏幕上还有触点(手指)
//				case MotionEvent.ACTION_POINTER_UP:
//					mode = 0;
//					break;
//				case MotionEvent.ACTION_MOVE:
//					if (mode == MODE_DRAG) { // 平移
//						float X = 0.5f;
//						float Y = 0.5f;
//						X = X - (event.getX() - xLastMove) / (getWidth());
//						Y = Y + (event.getY() - yLastMove) / (getHeight());
//						float[] fPosition = new float[]{X, Y};
//						NetDEVSDK.Scale(scaleEx, fPosition[0], fPosition[1], m_dwWinIndex);
//						xLastMove = event.getX();
//						yLastMove = event.getY();
//					} else if (mode == MODE_ZOOM) { // 缩放
//						mCurrentLength = (float) Math.sqrt(Math.pow(event.getX(0) - event.getX(1), 2) + Math.pow(event.getY(0) - event.getY(1), 2));
//						if (Math.abs(mCurrentLength - mOriginalLength) > 10) {
//							mCurrentRate = mOldRate * (mCurrentLength / mOriginalLength);
//							// 通过两个手指间距离判断放大还是缩小
//							boolean isLager = (mCurrentLength > mOriginalLength);
//							if (isLager) {
//								scaleEx += 0.1f;
//								// 0.05为误差
//								if (scaleEx >= 10.05f) {
//									scaleEx = 10.0f;
//								} else {
//									// 根据中心点放大
//									NetDEVSDK.Scale(scaleEx, fCenterPosition[0], fCenterPosition[1], m_dwWinIndex);
//								}
//							} else {
//								float[] fPosition = new float[]{0.5f, 0.5f};
//								scaleEx -= 0.1f;
//								// 0.05为误差
//								if (scaleEx <= 0.95f) {
//									scaleEx = 1.0f;
//								} else {
//									// 通过画面中心缩小，防止画面位置飘走
//									NetDEVSDK.Scale(scaleEx, fPosition[0], fPosition[1], m_dwWinIndex);
//								}
//							}
//							mOriginalLength = mCurrentLength;
//							mOldRate = mCurrentRate;
//							fTotal = 0.0f;
//						}
//					}
//					break;
//				case MotionEvent.ACTION_UP:
//					NetDEVSDK.gdwWinIndex = m_dwWinIndex;
//					break;
//			}
			return true;
		}
	};

	public CPlayView(Context context, AttributeSet attrs) {
		super(context, attrs);
		setEGLContextFactory(new ContextFactory());
		setEGLConfigChooser(new ConfigChooser(5, 6, 5, 0, 0, 0));
		setRenderer(new Renderer());
		setOnTouchListener(onTouchListener);
	}

	public CPlayView(Context context) {
		super(context);
	}

	private static class ContextFactory implements EGLContextFactory {
		private static final int EGL_CONTEXT_CLIENT_VERSION = 0x3098;

		public EGLContext createContext(EGL10 egl, EGLDisplay display, EGLConfig eglConfig) {
			int[] attrib_list = {EGL_CONTEXT_CLIENT_VERSION, 2, EGL10.EGL_NONE};
			return egl.eglCreateContext(display, eglConfig, EGL10.EGL_NO_CONTEXT, attrib_list);
		}

		public void destroyContext(EGL10 egl, EGLDisplay display, EGLContext context) {
			egl.eglDestroyContext(display, context);
		}
	}

	private static class ConfigChooser implements EGLConfigChooser {
		private static final int EGL_OPENGL_ES2_BIT = 4;
		private static final int[] s_configAttribs2 = {EGL10.EGL_RED_SIZE, 4, EGL10.EGL_GREEN_SIZE, 4, EGL10.EGL_BLUE_SIZE, 4, EGL10.EGL_RENDERABLE_TYPE, EGL_OPENGL_ES2_BIT, EGL10.EGL_NONE};

		public ConfigChooser(int r, int g, int b, int a, int depth, int stencil) {
			m_iRedSize = r;
			m_iGreenSize = g;
			m_iBlueSize = b;
			m_iAlphaSize = a;
			m_iDepthSize = depth;
			m_iStencilSize = stencil;
		}

		public EGLConfig chooseConfig(EGL10 egl, EGLDisplay display) {
			int[] num_config = new int[1];
			egl.eglChooseConfig(display, s_configAttribs2, null, 0, num_config);
			int numConfigs = num_config[0];
			if (numConfigs <= 0) {
				throw new IllegalArgumentException("No configs match configSpec");
			}
			EGLConfig[] configs = new EGLConfig[numConfigs];
			egl.eglChooseConfig(display, s_configAttribs2, configs, numConfigs, num_config);
			return chooseConfig(egl, display, configs);
		}

		public EGLConfig chooseConfig(EGL10 egl, EGLDisplay display, EGLConfig[] configs) {
			for (EGLConfig config : configs) {
				int d = findConfigAttrib(egl, display, config, EGL10.EGL_DEPTH_SIZE, 0);
				int s = findConfigAttrib(egl, display, config, EGL10.EGL_STENCIL_SIZE, 0);
				if (d < m_iDepthSize || s < m_iStencilSize) continue;
				int r = findConfigAttrib(egl, display, config, EGL10.EGL_RED_SIZE, 0);
				int g = findConfigAttrib(egl, display, config, EGL10.EGL_GREEN_SIZE, 0);
				int b = findConfigAttrib(egl, display, config, EGL10.EGL_BLUE_SIZE, 0);
				int a = findConfigAttrib(egl, display, config, EGL10.EGL_ALPHA_SIZE, 0);

				if (r == m_iRedSize && g == m_iGreenSize && b == m_iBlueSize && a == m_iAlphaSize)
					return config;
			}
			return null;
		}

		private int findConfigAttrib(EGL10 egl, EGLDisplay display, EGLConfig config, int attribute, int defaultValue) {
			if (egl.eglGetConfigAttrib(display, config, attribute, mValue)) {
				return mValue[0];
			}
			return defaultValue;
		}

		protected int m_iRedSize;
		protected int m_iGreenSize;
		protected int m_iBlueSize;
		protected int m_iAlphaSize;
		protected int m_iDepthSize;
		protected int m_iStencilSize;
		private final int[] mValue = new int[1];
	}

	private class Renderer implements GLSurfaceView.Renderer {
		public Renderer() {
			super();
			int ret = NetDEVSDK.initialize();
			if (ret == 1) {
				m_bRenderInited = true;
			}
		}

		public void onDrawFrame(GL10 gl) {
			if (m_bCanDrawFrame && m_bRenderInited) {
				NetDEVSDK.rendererRender(m_dwWinIndex);
			} else {
				// 清屏
				gl.glClear(GL10.GL_COLOR_BUFFER_BIT);
			}
		}

		@Override
		public void onSurfaceChanged(GL10 gl, int width, int height) {
			NetDEVSDK.setRendererViewport(width, height);
		}

		@Override
		public void onSurfaceCreated(GL10 gl, EGLConfig config) {
			NetDEVSDK.initializeRenderer(m_dwWinIndex);
		}
	}

	// 定义接口
	public interface OnTouchChangeListener {
		void onTouchChange(MotionEvent event);
	}

	public void setOnTouchChangeListener(OnTouchChangeListener onTouchChangeListener) {
		this.onTouchChangeListener = onTouchChangeListener;
	}

	public void disableGestureControl() {
		setOnTouchListener(null);
	}
}
