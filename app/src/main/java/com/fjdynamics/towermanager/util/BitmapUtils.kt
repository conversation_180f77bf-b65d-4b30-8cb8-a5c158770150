package com.fjdynamics.towermanager.util

import kotlin.math.roundToInt

/**
 * Bitmap相关工具类
 *
 * <AUTHOR>
 * @since 2025/7/7
 */
object BitmapUtils {

	fun calculateInSampleSize(
		originWidth: Int,
		originHeight: Int,
		targetWidth: Int = 720,
		targetHeight: Int = 576,
	): Int {
		var inSampleSize = 1

		if (originWidth > targetWidth || originHeight > targetHeight) {
			val widthRatio = (originWidth.toFloat() / targetWidth.toFloat()).roundToInt()
			val heightRatio = (originHeight.toFloat() / targetHeight.toFloat()).roundToInt()

			inSampleSize = minOf(widthRatio, heightRatio)

			val maxRatio = maxOf(widthRatio, heightRatio).toFloat()
			while ((inSampleSize * 2) <= maxRatio) {
				inSampleSize *= 2
			}
		}

		return inSampleSize
	}
}
