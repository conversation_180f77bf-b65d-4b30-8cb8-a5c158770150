<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
	android:layout_width="match_parent"
	android:layout_height="48dp">

	<androidx.appcompat.widget.AppCompatImageView
		android:id="@+id/iv_icon"
		android:layout_width="24dp"
		android:layout_height="24dp"
		android:layout_marginStart="21dp"
		android:src="@drawable/ic_avatar_default"
		app:layout_constraintBottom_toBottomOf="parent"
		app:layout_constraintStart_toStartOf="parent"
		app:layout_constraintTop_toTopOf="parent" />

	<androidx.appcompat.widget.AppCompatTextView
		android:id="@+id/tv_member_name"
		android:layout_width="wrap_content"
		android:layout_height="wrap_content"
		android:layout_marginStart="8dp"
		android:singleLine="true"
		android:textColor="#0E0E0E"
		android:textSize="18sp"
		android:textStyle="bold"
		app:layout_constraintBottom_toBottomOf="@+id/iv_icon"
		app:layout_constraintStart_toEndOf="@+id/iv_icon"
		app:layout_constraintTop_toTopOf="@+id/iv_icon"
		tools:text="李建国" />

	<com.google.android.material.imageview.ShapeableImageView
		android:id="@+id/iv_mic"
		android:layout_width="24dp"
		android:layout_height="24dp"
		android:layout_marginEnd="20dp"
		android:background="#1A0091FA"
		android:src="@drawable/ic_mic_blue"
		android:visibility="gone"
		app:contentPadding="5dp"
		app:layout_constraintBottom_toBottomOf="parent"
		app:layout_constraintEnd_toEndOf="parent"
		app:layout_constraintTop_toTopOf="parent"
		app:shapeAppearanceOverlay="@style/CircularShapeImageView"
		tools:visibility="visible" />

	<View
		android:layout_width="match_parent"
		android:layout_height="1dp"
		android:layout_marginHorizontal="21dp"
		android:background="#E6E6E6"
		app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
