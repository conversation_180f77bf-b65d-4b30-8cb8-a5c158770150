<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
	android:layout_width="match_parent"
	android:layout_height="83dp"
	android:layout_marginBottom="10dp"
	android:background="@drawable/shape_channel_bg_normal">

	<androidx.appcompat.widget.AppCompatTextView
		android:id="@+id/tv_channel_name"
		android:layout_width="wrap_content"
		android:layout_height="wrap_content"
		android:layout_marginStart="21dp"
		android:layout_marginTop="10dp"
		android:singleLine="true"
		android:textColor="#191923"
		android:textSize="21sp"
		app:layout_constraintStart_toStartOf="parent"
		app:layout_constraintTop_toTopOf="parent"
		tools:text="西沙塔吊01频道" />

	<androidx.appcompat.widget.AppCompatTextView
		android:id="@+id/tv_channel_id"
		android:layout_width="wrap_content"
		android:layout_height="wrap_content"
		android:layout_marginTop="6dp"
		android:singleLine="true"
		android:textColor="#999999"
		android:textSize="17sp"
		app:layout_constraintStart_toStartOf="@+id/tv_channel_name"
		app:layout_constraintTop_toBottomOf="@+id/tv_channel_name"
		tools:text="ID:101897" />

	<com.google.android.material.imageview.ShapeableImageView
		android:id="@+id/iv_mic"
		android:layout_width="50dp"
		android:layout_height="50dp"
		android:layout_marginEnd="17dp"
		android:src="@drawable/ic_mic_blue"
		android:visibility="gone"
		app:contentPadding="12dp"
		app:layout_constraintBottom_toBottomOf="parent"
		app:layout_constraintEnd_toEndOf="parent"
		app:layout_constraintTop_toTopOf="parent"
		app:shapeAppearanceOverlay="@style/CircularShapeImageView"
		tools:visibility="visible" />
</androidx.constraintlayout.widget.ConstraintLayout>
