<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
	android:layout_width="match_parent"
	android:layout_height="match_parent">

	<!-- 视频容器，用于支持手势缩放和拖拽 -->
	<FrameLayout
		android:id="@+id/fl_video_container"
		android:layout_width="match_parent"
		android:layout_height="match_parent"
		app:layout_constraintStart_toStartOf="parent"
		app:layout_constraintTop_toTopOf="parent">

		<androidx.media3.ui.PlayerView
			android:id="@+id/player_view"
			android:layout_width="match_parent"
			android:layout_height="match_parent"
			app:use_controller="false"
			app:resize_mode="fit"
			app:surface_type="surface_view" />

	</FrameLayout>

	<!--	<com.fjdynamics.towermanager.widget.PointPicker-->
	<!--		android:id="@+id/point_picker"-->
	<!--		android:layout_width="match_parent"-->
	<!--		android:layout_height="match_parent" />-->

	<com.fjdynamics.towermanager.widget.roundLayout.RoundLinearLayout
		android:id="@+id/ll_back"
		android:layout_width="@dimen/dimen_59dp"
		android:layout_height="@dimen/dimen_59dp"
		android:layout_margin="@dimen/dimen_21dp"
		android:background="@drawable/selector_button_pressed"
		android:gravity="center"
		android:orientation="vertical"
		app:layout_constraintStart_toStartOf="parent"
		app:layout_constraintTop_toTopOf="parent"
		app:rRadius="@dimen/dimen_5dp"
		app:rStrokeColor="@color/white"
		app:rStrokeWidth="@dimen/dimen_1dp">

		<ImageView
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:src="@drawable/ic_back" />
	</com.fjdynamics.towermanager.widget.roundLayout.RoundLinearLayout>

	<com.fjdynamics.towermanager.widget.roundLayout.RoundLinearLayout
		android:id="@+id/con_battery"
		android:layout_width="wrap_content"
		android:layout_height="@dimen/dimen_48dp"
		android:layout_marginTop="@dimen/dimen_27dp"
		android:gravity="center"
		android:minWidth="@dimen/dimen_105dp"
		android:paddingStart="@dimen/dimen_16dp"
		android:paddingEnd="@dimen/dimen_16dp"
		android:visibility="gone"
		app:layout_constraintEnd_toStartOf="@id/ll_camera"
		app:layout_constraintTop_toTopOf="parent"
		app:rRadius="@dimen/dimen_5dp"
		tools:visibility="visible">

		<LinearLayout
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:gravity="center"
			android:orientation="horizontal">

			<LinearLayout
				android:id="@+id/ll_battery"
				android:layout_width="27dp"
				android:layout_height="16dp"
				android:background="@drawable/ic_bg"
				android:gravity="center_vertical"
				android:orientation="horizontal">

				<ProgressBar
					android:id="@+id/progress_bar"
					style="?android:attr/progressBarStyleHorizontal"
					android:layout_width="17dp"
					android:layout_height="8dp"
					android:layout_marginStart="4dp"
					android:max="100"
					android:progress="100" />
			</LinearLayout>

			<ImageView
				android:id="@+id/iv_battery"
				android:layout_width="wrap_content"
				android:layout_height="wrap_content"
				android:src="@drawable/ic_battery_charge"
				android:visibility="gone" />

			<TextView
				android:id="@+id/tv_battery"
				android:layout_width="wrap_content"
				android:layout_height="wrap_content"
				android:layout_marginStart="@dimen/dimen_4dp"
				android:textColor="@color/white"
				android:textSize="@dimen/txt_16sp" />
		</LinearLayout>

	</com.fjdynamics.towermanager.widget.roundLayout.RoundLinearLayout>

	<androidx.appcompat.widget.AppCompatImageView
		android:id="@+id/ll_camera"
		android:layout_width="@dimen/dimen_59dp"
		android:layout_height="@dimen/dimen_59dp"
		android:layout_marginTop="@dimen/dimen_21dp"
		android:layout_marginEnd="@dimen/dimen_29dp"
		android:orientation="vertical"
		android:padding="6dp"
		android:src="@drawable/ic_window_control"
		app:layout_constraintEnd_toEndOf="parent"
		app:layout_constraintTop_toTopOf="parent" />

	<com.google.android.material.imageview.ShapeableImageView
		android:id="@+id/iv_zoom_out"
		android:layout_width="59dp"
		android:layout_height="59dp"
		android:layout_marginEnd="29dp"
		android:layout_marginBottom="33dp"
		android:background="#CC191923"
		android:padding="1dp"
		android:src="@drawable/ic_minus"
		app:contentPadding="13dp"
		app:layout_constraintBottom_toBottomOf="parent"
		app:layout_constraintEnd_toStartOf="@id/round_menu_view"
		app:shapeAppearanceOverlay="@style/CircularShapeImageView"
		app:strokeColor="@color/white"
		app:strokeWidth="1dp" />

	<com.fjdynamics.towermanager.widget.RoundMenuView
		android:id="@+id/round_menu_view"
		android:layout_width="@dimen/dimen_171dp"
		android:layout_height="@dimen/dimen_171dp"
		android:layout_marginEnd="@dimen/dimen_32dp"
		android:layout_marginBottom="@dimen/dimen_33dp"
		android:visibility="gone"
		app:layout_constraintBottom_toBottomOf="parent"
		app:layout_constraintEnd_toEndOf="parent"
		tools:visibility="visible" />

	<com.google.android.material.imageview.ShapeableImageView
		android:id="@+id/iv_zoom_in"
		android:layout_width="59dp"
		android:layout_height="59dp"
		android:layout_marginEnd="@dimen/dimen_32dp"
		android:layout_marginBottom="@dimen/dimen_21dp"
		android:background="#CC191923"
		android:padding="1dp"
		android:src="@drawable/ic_add"
		app:contentPadding="13dp"
		app:layout_constraintBottom_toBottomOf="parent"
		app:layout_constraintBottom_toTopOf="@id/round_menu_view"
		app:layout_constraintEnd_toEndOf="parent"
		app:shapeAppearanceOverlay="@style/CircularShapeImageView"
		app:strokeColor="@color/white"
		app:strokeWidth="1dp" />

	<ImageView
		android:id="@+id/iv_zoom_mode"
		android:layout_width="wrap_content"
		android:layout_height="20dp"
		android:layout_marginStart="24dp"
		android:layout_marginBottom="17dp"
		android:visibility="gone"
		app:layout_constraintBottom_toBottomOf="parent"
		app:layout_constraintStart_toStartOf="parent"
		tools:src="@drawable/zoom_mode_auto"
		tools:visibility="visible" />
</androidx.constraintlayout.widget.ConstraintLayout>
