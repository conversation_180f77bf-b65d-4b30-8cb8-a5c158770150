<resources>

	<style name="dialog" parent="@android:style/Theme.Dialog">
		<!--边框-->
		<item name="android:windowFrame">@null</item>
		<!--是否浮现在activity之上-->
		<item name="android:windowIsFloating">true</item>
		<!--半透明-->
		<item name="android:windowIsTranslucent">true</item>
		<!--无标题-->
		<item name="android:windowNoTitle">true</item>
		<!--背景透明-->
		<item name="android:windowBackground">@android:color/transparent</item>
		<!--模糊-->
		<item name="android:backgroundDimEnabled">true</item>
	</style>

	<style name="dialog_full_screen" parent="@android:style/Theme.Dialog">
		<!--边框-->
		<item name="android:windowFrame">@null</item>
		<!--是否浮现在activity之上-->
		<item name="android:windowIsFloating">false</item>
		<!--半透明-->
		<item name="android:windowIsTranslucent">true</item>
		<!--无标题-->
		<item name="android:windowNoTitle">true</item>
		<!--背景透明-->
		<item name="android:windowBackground">@android:color/transparent</item>
		<!--模糊-->
		<item name="android:backgroundDimEnabled">true</item>
		<!-- 弹窗是否全屏 -->
		<item name="android:windowFullscreen">true</item>
	</style>

	<style name="dialog_live" parent="@android:style/Theme.Dialog">
		<!--边框-->
		<item name="android:windowFrame">@null</item>
		<!--是否浮现在activity之上-->
		<item name="android:windowIsFloating">true</item>
		<!--半透明-->
		<item name="android:windowIsTranslucent">true</item>
		<!--无标题-->
		<item name="android:windowNoTitle">true</item>
		<!--背景透明-->
		<item name="android:windowBackground">@android:color/transparent</item>
		<!--模糊-->
		<item name="android:backgroundDimEnabled">false</item>
	</style>

	<style name="VideoWallImageStyle">
		<item name="android:layout_width">0dp</item>
		<item name="android:layout_height">0dp</item>
	</style>

	<style name="DelayConfigButton">
		<item name="android:layout_width">match_parent</item>
		<item name="android:gravity">center</item>
		<item name="android:layout_height">wrap_content</item>
	</style>

	<style name="CircularShapeImageView">
		<item name="cornerFamily">rounded</item>
		<item name="cornerSize">50%</item>
	</style>

</resources>
